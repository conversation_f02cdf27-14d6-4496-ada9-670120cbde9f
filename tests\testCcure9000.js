/**
 * CCURE9000 Integration Test Runner
 * 
 * This script demonstrates and tests the complete CCURE9000 XML outbound integration.
 * It simulates the workflow from identity data creation to XML file generation.
 * 
 * Usage: node tests/testCcure9000.js
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');

// Import the handler
const { generateXmlData } = require('../handlers/generateXmlData.handler');

// Test configuration
const TEST_OUTPUT_DIR = path.join(__dirname, '..', 'temp', 'ccure9000_demo');

// Sample identity data (simulating data from the Identity model)
const sampleIdentityData = {
  identity_id: uuidv4(),
  eid: 'EMP001',
  email: '<EMAIL>',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  middle_name: '<PERSON>',
  national_id: '*********',
  mobile: '+*********0',
  company: 'CareMate Solutions',
  organization: 'IT Department',
  company_code: 'CMS001',
  job_title: 'Senior Software Engineer',
  job_code: 'SSE001',
  start_date: '2024-01-01T00:00:00.000Z',
  end_date: '2025-12-31T23:59:59.000Z',
  status: 1,
  identity_type: 1,
  suffix: 'Jr.',
  manager: uuidv4(),
  created_at: new Date(),
  updated_at: new Date()
};

// Mock agent configuration
const mockAgent = {
  agent_id: uuidv4(),
  name: 'ccure9000_xml_outbound_demo',
  display_name: 'CCURE9000 XML Demo Agent',
  source: 'Local',
  queue: 'ccure9000_xml_outbound_queue',
  mapping: 'ccure9000Outbound',
  handler: 'generateXmlData',
  type: 'Outbound',
  settingsObj: {
    directory_path: TEST_OUTPUT_DIR
  }
};



// Setup test environment
function setupTestEnvironment() {
  console.log('🔧 Setting up CCURE9000 demo environment...');
  
  // Create test directories
  if (!fs.existsSync(TEST_OUTPUT_DIR)) {
    fs.mkdirSync(TEST_OUTPUT_DIR, { recursive: true });
    console.log(`   ✓ Created output directory: ${TEST_OUTPUT_DIR}`);
  }
  

  
  console.log('✅ Demo environment setup complete\n');
}

// Display sample data
function displaySampleData() {
  console.log('📋 Sample Identity Data:');
  console.log('========================');
  console.log(`Employee ID: ${sampleIdentityData.eid}`);
  console.log(`Name: ${sampleIdentityData.first_name} ${sampleIdentityData.middle_name} ${sampleIdentityData.last_name}`);
  console.log(`Email: ${sampleIdentityData.email}`);
  console.log(`Company: ${sampleIdentityData.company} (${sampleIdentityData.company_code})`);
  console.log(`Department: ${sampleIdentityData.organization}`);
  console.log(`Job Title: ${sampleIdentityData.job_title} (${sampleIdentityData.job_code})`);
  console.log(`Start Date: ${sampleIdentityData.start_date}`);
  console.log(`End Date: ${sampleIdentityData.end_date}`);
  console.log('');
}

// Test single record processing
async function testSingleRecord() {
  console.log('🧪 Test 1: Single Record Processing');
  console.log('====================================');
  
  try {
    const mockEvent = {
      params: sampleIdentityData,
      trace_id: uuidv4(),
      event_type: 'ccure9000_xml_outbound'
    };
    
    console.log('   📤 Processing single identity record...');
    
    const result = await generateXmlData({
      event: mockEvent,
      agent: mockAgent,
      performanceMonitor: null,
      messageId: uuidv4()
    });
    
    console.log('   📊 Processing Results:');
    console.log(`      Total Records: ${result.totalRecords}`);
    console.log(`      Successful: ${result.successfulRecords}`);
    console.log(`      Failed: ${result.failedRecords}`);
    console.log(`      Files Generated: ${result.filesGenerated}`);
    console.log(`      Errors: ${result.errors.length}`);
    
    if (result.errors.length > 0) {
      console.log('   ❌ Errors encountered:');
      result.errors.forEach((error, index) => {
        console.log(`      ${index + 1}. ${error.message || error}`);
      });
    } else {
      console.log('   ✅ Single record processing completed successfully');
    }
    
    return result;
    
  } catch (error) {
    console.error('   ❌ Error in single record test:', error.message);
    return null;
  }
}

// Test batch processing
async function testBatchProcessing() {
  console.log('\n🧪 Test 2: Batch Processing');
  console.log('============================');
  
  try {
    // Create batch data with multiple employees
    const batchData = [
      sampleIdentityData,
      {
        ...sampleIdentityData,
        identity_id: uuidv4(),
        eid: 'EMP002',
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        middle_name: 'Elizabeth',
        job_title: 'Project Manager',
        job_code: 'PM001'
      },
      {
        ...sampleIdentityData,
        identity_id: uuidv4(),
        eid: 'EMP003',
        email: '<EMAIL>',
        first_name: 'Bob',
        last_name: 'Johnson',
        middle_name: '',
        job_title: 'Security Analyst',
        job_code: 'SA001'
      }
    ];
    
    const mockEvent = {
      params: batchData,
      trace_id: uuidv4(),
      event_type: 'ccure9000_xml_outbound'
    };
    
    console.log(`   📤 Processing batch of ${batchData.length} identity records...`);
    
    const result = await generateXmlData({
      event: mockEvent,
      agent: mockAgent,
      performanceMonitor: null,
      messageId: uuidv4()
    });
    
    console.log('   📊 Batch Processing Results:');
    console.log(`      Total Records: ${result.totalRecords}`);
    console.log(`      Successful: ${result.successfulRecords}`);
    console.log(`      Failed: ${result.failedRecords}`);
    console.log(`      Files Generated: ${result.filesGenerated}`);
    console.log(`      Errors: ${result.errors.length}`);
    
    if (result.errors.length > 0) {
      console.log('   ❌ Errors encountered:');
      result.errors.forEach((error, index) => {
        console.log(`      ${index + 1}. ${error.message || error}`);
      });
    } else {
      console.log('   ✅ Batch processing completed successfully');
    }
    
    return result;
    
  } catch (error) {
    console.error('   ❌ Error in batch processing test:', error.message);
    return null;
  }
}

// Display generated files
function displayGeneratedFiles() {
  console.log('\n📁 Generated Files:');
  console.log('===================');
  
  try {
    if (!fs.existsSync(TEST_OUTPUT_DIR)) {
      console.log('   ❌ Output directory does not exist');
      return;
    }
    
    const files = fs.readdirSync(TEST_OUTPUT_DIR);
    const xmlFiles = files.filter(file => file.endsWith('.xml'));
    
    if (xmlFiles.length === 0) {
      console.log('   ❌ No XML files found');
      return;
    }
    
    xmlFiles.forEach((file, index) => {
      const filePath = path.join(TEST_OUTPUT_DIR, file);
      const stats = fs.statSync(filePath);
      
      console.log(`   ${index + 1}. ${file}`);
      console.log(`      Size: ${stats.size} bytes`);
      console.log(`      Created: ${stats.birthtime.toISOString()}`);
      console.log(`      Path: ${filePath}`);
      
      // Show first few lines of XML content
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').slice(0, 5);
      console.log('      Preview:');
      lines.forEach(line => {
        if (line.trim()) {
          console.log(`        ${line.trim()}`);
        }
      });
      console.log('        ...');
      console.log('');
    });
    
  } catch (error) {
    console.error('   ❌ Error reading generated files:', error.message);
  }
}

// Main test runner
async function runDemo() {
  console.log('🚀 CCURE9000 XML Integration Demo');
  console.log('=================================\n');
  
  // Setup environment
  setupTestEnvironment();
  
  // Display sample data
  displaySampleData();
  
  // Run tests
  const singleResult = await testSingleRecord();
  const batchResult = await testBatchProcessing();
  
  // Display generated files
  displayGeneratedFiles();
  
  // Summary
  console.log('📈 Demo Summary:');
  console.log('================');
  console.log(`✓ Single Record Test: ${singleResult ? 'PASSED' : 'FAILED'}`);
  console.log(`✓ Batch Processing Test: ${batchResult ? 'PASSED' : 'FAILED'}`);
  console.log(`✓ Output Directory: ${TEST_OUTPUT_DIR}`);
  console.log('');
  console.log('🎉 CCURE9000 XML Integration Demo Complete!');
  console.log('');
  console.log('Next Steps:');
  console.log('1. Review generated XML files in the output directory');
  console.log('2. Configure CCURE9000 to monitor the XML drop folder');
  console.log('3. Set up the agent in your environment using the seeder');
  console.log('4. Test with real identity data from your database');
}

// Run the demo
if (require.main === module) {
  runDemo().catch(error => {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  });
}

module.exports = { runDemo };
