# Rules Configuration Guide

## Overview
The trigger plugin now supports both single event and multiple events per rule, allowing you to reduce code duplication while maintaining flexibility.

## Supported Formats

### 1. Single Event (Legacy Format)
```json
{
  "name": "Single Event Rule",
  "conditions": {
    "all": [
      {
        "fact": "email",
        "operator": "notEqual",
        "value": null
      }
    ]
  },
  "event": {
    "type": "notification",
    "params": {
      "message": "User created"
    }
  },
  "order": 1
}
```

### 2. Multiple Events (New Format)
```json
{
  "name": "Multiple Events Rule",
  "conditions": {
    "all": [
      {
        "fact": "email",
        "operator": "notEqual",
        "value": null
      }
    ]
  },
  "events": [
    {
      "type": "notification",
      "params": {
        "message": "User created"
      }
    },
    {
      "type": "api_1_outbound"
    },
    {
      "type": "api_2_outbound"
    }
  ],
  "order": 1
}
```

## Benefits

### Before (Duplicated Rules)
```json
[
  {
    "name": "Identity Modified",
    "conditions": { /* same conditions */ },
    "event": { "type": "notification" },
    "order": 1
  },
  {
    "name": "Identity Modified",
    "conditions": { /* same conditions */ },
    "event": { "type": "api_1_outbound" },
    "order": 1
  },
  {
    "name": "Identity Modified",
    "conditions": { /* same conditions */ },
    "event": { "type": "api_2_outbound" },
    "order": 1
  }
]
```

### After (Optimized Rules)
```json
[
  {
    "name": "Identity Modified",
    "conditions": { /* conditions defined once */ },
    "events": [
      { "type": "notification" },
      { "type": "api_1_outbound" },
      { "type": "api_2_outbound" }
    ],
    "order": 1
  }
]
```

## How It Works

1. **Rule Processing**: The trigger plugin automatically detects rules with `events` arrays
2. **Rule Expansion**: Each rule with multiple events is expanded into individual rules
3. **Naming Convention**: Expanded rules are named `{original_name}_Event_{index}`
4. **Backward Compatibility**: Existing rules with single `event` continue to work

## Rule Statistics

The system now provides detailed statistics about rule processing:
- Total rules processed
- Number of single-event vs multi-event rules
- Total events generated
- Unique event types

## Validation

The system includes comprehensive validation:
- Rule structure validation
- Event type validation
- Condition validation
- Operator validation

## Example Identity Rules

The identity rules have been optimized from 4 duplicate rules to 2 consolidated rules:

1. **Identity Basic Processing**: Handles notification, api_1_outbound, and api_2_outbound
2. **Identity CCURE9000 XML Export**: Handles ccure9000_xml_outbound (requires EID)

## Migration Guide

To migrate existing rules:

1. **Identify Duplicate Rules**: Find rules with identical conditions but different events
2. **Consolidate Conditions**: Create a single rule with the shared conditions
3. **Create Events Array**: Move all event objects into an `events` array
4. **Update Rule Name**: Use a descriptive name that covers all events
5. **Test**: Verify the new rules generate the same events as before

## Performance Benefits

- **Reduced File Size**: Fewer lines of JSON configuration
- **Easier Maintenance**: Single place to update conditions
- **Better Readability**: Clear relationship between conditions and events
- **Faster Processing**: Less rule parsing overhead
