const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');
const { uploadFile } = require('../services/csv.service');

/**
 * Transforms object properties using template strings and context data
 * @param {Object} template - Template object with placeholder strings
 * @param {Object} data - Data object to extract values from
 * @param {Object} context - Additional context data
 * @returns {Object} Transformed object
 */
function transformObject(template, data, context = {}) {
  if (typeof template === 'string') {
    // Handle template strings like {{Identity.email}}
    return template.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const keys = path.split('.');
      let value = data;
      
      // Handle special context variables
      if (keys[0] === 'current_timestamp') {
        return new Date().toISOString();
      }
      if (keys[0] === 'batch_id') {
        return context.batchId || uuidv4();
      }
      if (keys[0] === 'batch_size') {
        return context.batchSize || 1;
      }
      
      // Navigate through the object path
      for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
          value = value[key];
        } else {
          return ''; // Return empty string if path doesn't exist
        }
      }
      
      return value || '';
    });
  }
  
  if (Array.isArray(template)) {
    return template.map(item => transformObject(item, data, context));
  }
  
  if (typeof template === 'object' && template !== null) {
    const result = {};
    for (const [key, value] of Object.entries(template)) {
      result[key] = transformObject(value, data, context);
    }
    return result;
  }
  
  return template;
}

/**
 * Generates XML content from transformed data
 * @param {Object} data - Transformed data object
 * @param {Object} xmlConfig - XML configuration from mapping
 * @returns {string} Generated XML content
 */
function generateXmlContent(data, xmlConfig) {
  const { rootElement, xmlDeclaration, namespace } = xmlConfig;
  
  let xml = '';
  
  // Add XML declaration
  if (xmlDeclaration) {
    xml += `<?xml version="${xmlDeclaration.version || '1.0'}" encoding="${xmlDeclaration.encoding || 'UTF-8'}"?>\n`;
  }
  
  // Add root element with namespace if specified
  const rootAttrs = namespace ? ` xmlns="${namespace}"` : '';
  xml += `<${rootElement}${rootAttrs}>\n`;
  
  // Generate XML content recursively
  xml += generateXmlElements(data, 1);
  
  xml += `</${rootElement}>\n`;
  
  return xml;
}

/**
 * Recursively generates XML elements from data object
 * @param {*} data - Data to convert to XML
 * @param {number} indent - Current indentation level
 * @returns {string} XML elements
 */
function generateXmlElements(data, indent = 0) {
  const indentStr = '  '.repeat(indent);
  let xml = '';
  
  if (Array.isArray(data)) {
    // Handle arrays
    data.forEach(item => {
      xml += generateXmlElements(item, indent);
    });
  } else if (typeof data === 'object' && data !== null) {
    // Handle objects
    Object.entries(data).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        // Handle array values
        value.forEach(item => {
          xml += `${indentStr}<${key}>\n`;
          xml += generateXmlElements(item, indent + 1);
          xml += `${indentStr}</${key}>\n`;
        });
      } else if (typeof value === 'object' && value !== null) {
        // Handle nested objects
        xml += `${indentStr}<${key}>\n`;
        xml += generateXmlElements(value, indent + 1);
        xml += `${indentStr}</${key}>\n`;
      } else {
        // Handle primitive values
        const escapedValue = escapeXml(String(value || ''));
        xml += `${indentStr}<${key}>${escapedValue}</${key}>\n`;
      }
    });
  }
  
  return xml;
}

/**
 * Escapes XML special characters
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeXml(text) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

/**
 * Validates transformed data against validation rules
 * @param {Object} data - Data to validate
 * @param {Object} validation - Validation configuration
 * @returns {Object} Validation result
 */
function validateData(data, validation) {
  const errors = [];
  
  if (!validation) {
    return { isValid: true, errors: [] };
  }
  
  // Check required fields
  if (validation.required) {
    validation.required.forEach(fieldPath => {
      const value = getNestedValue(data, fieldPath);
      if (value === undefined || value === null || value === '') {
        errors.push(`Required field '${fieldPath}' is missing or empty`);
      }
    });
  }
  
  // Check field rules
  if (validation.rules) {
    Object.entries(validation.rules).forEach(([fieldPath, rules]) => {
      const value = getNestedValue(data, fieldPath);
      
      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push(`Required field '${fieldPath}' is missing or empty`);
      }
      
      if (value !== undefined && value !== null && value !== '') {
        if (rules.type === 'email' && !isValidEmail(value)) {
          errors.push(`Field '${fieldPath}' must be a valid email address`);
        }
        
        if (rules.type === 'string' && typeof value !== 'string') {
          errors.push(`Field '${fieldPath}' must be a string`);
        }
        
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(`Field '${fieldPath}' must be at least ${rules.minLength} characters long`);
        }
        
        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(`Field '${fieldPath}' must be no more than ${rules.maxLength} characters long`);
        }
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets nested value from object using dot notation
 * @param {Object} obj - Object to search in
 * @param {string} path - Dot notation path
 * @returns {*} Found value or undefined
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Validates email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Writes XML content to temporary file and uploads to destination
 * @param {string} xmlContent - XML content to write
 * @param {string} filename - Name of the file
 * @param {Object} agent - Agent configuration
 * @param {Object} performanceMonitor - Performance monitor instance
 * @returns {Promise<Object>} Upload result
 */
async function writeAndUploadXmlFile(xmlContent, filename, agent, performanceMonitor) {
  const tempDir = path.join(process.cwd(), 'temp', 'xml');
  const tempFilePath = path.join(tempDir, filename);

  try {
    // Ensure temp directory exists
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write to temporary file
    fs.writeFileSync(tempFilePath, xmlContent, 'utf8');

    logger.info(`[XML Handler] Temporary XML file created: ${tempFilePath}`);

    // Upload to destination using generic upload function
    const uploadResult = await uploadFile(tempFilePath, agent, performanceMonitor, 'XML');

    // Clean up temporary file
    try {
      fs.unlinkSync(tempFilePath);
      logger.debug(`[XML Handler] Temporary file cleaned up: ${tempFilePath}`);
    } catch (cleanupError) {
      logger.warn(`[XML Handler] Failed to cleanup temporary file: ${tempFilePath}`, cleanupError.message);
    }

    return {
      success: uploadResult.success,
      filePath: uploadResult.uploadedPath,
      fileSize: uploadResult.fileSize,
      destination: uploadResult.destination,
      error: uploadResult.error,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    // Clean up temporary file on error
    try {
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    } catch (cleanupError) {
      logger.warn(`[XML Handler] Failed to cleanup temporary file on error: ${tempFilePath}`, cleanupError.message);
    }

    logger.error(`[XML Handler] Failed to write and upload XML file: ${filename}`, error);
    throw error;
  }
}

/**
 * Main handler for generating XML files from event data
 * @param {object} params - Handler parameters
 * @param {object} params.event - Event data from queue
 * @param {object} params.agent - Agent configuration
 * @param {object} params.performanceMonitor - Performance monitor instance
 * @param {string} params.messageId - Unique message identifier
 * @returns {Promise<object>} - Handler results
 */
const generateXmlData = async ({ event, agent, performanceMonitor = null, messageId = null }) => {
  const handlerResults = {
    agentName: agent.name,
    totalRecords: 0,
    successfulRecords: 0,
    failedRecords: 0,
    filesGenerated: 0,
    errors: [],
    messageId
  };

  try {
    logger.info(`[XML Handler] Processing event for agent: ${agent.name}`, { messageId });

    // Step 1: Load mapping configuration
    const mappingConfig = require(`../mappings/${agent.mapping}.mapping.json`);

    // Step 2: Process event data
    const eventData = event.params || event.data || event;
    const batchId = uuidv4();
    const context = {
      batchId,
      batchSize: Array.isArray(eventData) ? eventData.length : 1,
      timestamp: new Date().toISOString(),
      messageId
    };

    logger.info(`[XML Handler] Processing ${context.batchSize} record(s) for agent ${agent.name}`);

    // Step 4: Prepare data for transformation
    let dataForTransformation;
    if (Array.isArray(eventData)) {
      dataForTransformation = eventData;
      handlerResults.totalRecords = eventData.length;
    } else {
      dataForTransformation = [eventData];
      handlerResults.totalRecords = 1;
    }

    // Step 5: Transform data according to mapping
    performanceMonitor?.startStep('Data Transformation', {
      recordCount: handlerResults.totalRecords,
      batchId
    });

    const transformedData = transformObject(mappingConfig.dataTransform, {
      records: dataForTransformation,
      context
    }, context);

    performanceMonitor?.endStep('Data Transformation', {
      success: true,
      transformedDataSize: JSON.stringify(transformedData).length
    });

    logger.info(`[XML Handler] Data transformation completed for ${agent.name}`);

    // Step 6: Validate transformed data
    logger.debug(`[XML Handler] Validating transformed data:`, {
      dataKeys: Object.keys(transformedData),
      validationRules: mappingConfig.validation?.required || [],
      messageId
    });

    const validation = validateData(transformedData, mappingConfig.validation);
    if (!validation.isValid) {
      logger.error(`[XML Handler] Data validation failed for ${agent.name}:`, validation.errors);

      // Capture validation errors instead of throwing
      handlerResults.failedRecords = handlerResults.totalRecords;
      handlerResults.errors.push(...validation.errors);

      return handlerResults;
    }

    logger.info(`[XML Handler] Data validation passed for ${agent.name}`);

    // Step 7: Generate XML content
    performanceMonitor?.startStep('XML Generation', {
      batchId,
      xmlConfig: mappingConfig.xmlConfig
    });

    const xmlContent = generateXmlContent(transformedData, mappingConfig.xmlConfig);

    performanceMonitor?.endStep('XML Generation', {
      success: true,
      xmlSize: xmlContent.length
    });

    logger.info(`[XML Handler] XML content generated for ${agent.name}, size: ${xmlContent.length} bytes`);

    // Step 8: Generate filename and upload file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `CCURE9000_Personnel_${timestamp}_${batchId.substring(0, 8)}.xml`;

    performanceMonitor?.startStep('XML Upload', {
      filename,
      destination: agent.source,
      xmlSize: xmlContent.length
    });

    const uploadResult = await writeAndUploadXmlFile(xmlContent, filename, agent, performanceMonitor);

    performanceMonitor?.endStep('XML Upload', {
      success: uploadResult.success,
      fileSize: uploadResult.fileSize,
      destination: uploadResult.destination,
      uploadedPath: uploadResult.filePath
    });

    // Step 9: Update results
    if (uploadResult.success) {
      handlerResults.successfulRecords = handlerResults.totalRecords;
      handlerResults.filesGenerated = 1;

      logger.info(`[XML Handler] XML file uploaded successfully for ${agent.name}:`, {
        filename,
        destination: uploadResult.destination,
        uploadedPath: uploadResult.filePath,
        fileSize: uploadResult.fileSize,
        recordCount: handlerResults.totalRecords,
        messageId
      });
    } else {
      handlerResults.failedRecords = handlerResults.totalRecords;
      handlerResults.errors.push({
        type: 'upload_error',
        message: uploadResult.error || 'Failed to upload XML file'
      });

      logger.error(`[XML Handler] XML file upload failed for ${agent.name}:`, {
        filename,
        destination: uploadResult.destination,
        error: uploadResult.error,
        messageId
      });
    }

    return handlerResults;

  } catch (error) {
    logger.error(`[XML Handler] Error processing event for ${agent.name}:`, error);

    handlerResults.failedRecords = handlerResults.totalRecords;
    handlerResults.errors.push({
      type: 'processing_error',
      message: error.message,
      stack: error.stack
    });

    return handlerResults;
  }
};

module.exports = { generateXmlData };
