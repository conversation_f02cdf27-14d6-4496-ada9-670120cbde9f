/**
 * Rules Helper
 * 
 * Utilities for processing and validating rules configurations
 */

const logger = require("../config/logger");

/**
 * Validates a rule configuration
 * @param {Object} rule - The rule to validate
 * @returns {Boolean} - Whether the rule is valid
 */
const validateRule = (rule) => {
  if (!rule.name) {
    logger.warn("Rule missing name property");
    return false;
  }

  if (!rule.conditions) {
    logger.warn(`Rule '${rule.name}' missing conditions property`);
    return false;
  }

  if (!rule.event && !rule.events) {
    logger.warn(`Rule '${rule.name}' missing both event and events properties`);
    return false;
  }

  if (rule.events && !Array.isArray(rule.events)) {
    logger.warn(`Rule '${rule.name}' events property must be an array`);
    return false;
  }

  if (rule.events && rule.events.length === 0) {
    logger.warn(`Rule '${rule.name}' events array is empty`);
    return false;
  }

  return true;
};

/**
 * Normalizes rules to ensure consistent structure
 * @param {Array} rules - Array of rules to normalize
 * @returns {Array} - Normalized rules array
 */
const normalizeRules = (rules) => {
  if (!Array.isArray(rules)) {
    logger.error("Rules must be an array");
    return [];
  }

  return rules.filter(rule => validateRule(rule));
};

/**
 * Expands rules with multiple events into individual rules
 * @param {Array} rules - Array of rules to expand
 * @returns {Array} - Expanded rules array
 */
const expandMultiEventRules = (rules) => {
  const expandedRules = [];

  rules.forEach(rule => {
    if (rule.events && Array.isArray(rule.events)) {
      // Multiple events - create separate rules for each event
      rule.events.forEach((event, index) => {
        const expandedRule = {
          ...rule,
          name: `${rule.name}_Event_${index + 1}`,
          event: event,
          // Remove the events array from the expanded rule
          events: undefined
        };
        expandedRules.push(expandedRule);
      });
    } else {
      // Single event - add rule as is
      expandedRules.push(rule);
    }
  });

  return expandedRules;
};

/**
 * Gets rule statistics for debugging
 * @param {Array} rules - Array of rules
 * @returns {Object} - Statistics object
 */
const getRuleStats = (rules) => {
  const stats = {
    totalRules: rules.length,
    singleEventRules: 0,
    multiEventRules: 0,
    totalEvents: 0,
    eventTypes: new Set()
  };

  rules.forEach(rule => {
    if (rule.events && Array.isArray(rule.events)) {
      stats.multiEventRules++;
      stats.totalEvents += rule.events.length;
      rule.events.forEach(event => stats.eventTypes.add(event.type));
    } else if (rule.event) {
      stats.singleEventRules++;
      stats.totalEvents++;
      stats.eventTypes.add(rule.event.type);
    }
  });

  stats.uniqueEventTypes = stats.eventTypes.size;
  stats.eventTypes = Array.from(stats.eventTypes);

  return stats;
};

module.exports = {
  validateRule,
  normalizeRules,
  expandMultiEventRules,
  getRuleStats
};
